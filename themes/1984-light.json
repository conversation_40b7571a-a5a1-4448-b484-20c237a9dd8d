{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984 Light", "author": "converted from VS Code theme", "themes": [{"name": "1984 Light", "appearance": "light", "style": {"border": "#a8b6ca", "border.variant": "#a8b6ca", "border.focused": "#46BDFF", "border.selected": "#46BDFF", "border.transparent": "#a8b6ca", "border.disabled": "#a8b6ca", "elevated_surface.background": "#dfe0f0", "surface.background": "#dfe0f0", "background": "#e4e5f5", "element.background": "#46beff6b", "element.hover": "#46beff3f", "element.active": "#46beff6b", "element.selected": "#46beff6b", "ghost_element.hover": "#46beff3f", "drop_target.background": "#46beff12", "ghost_element.selected": "#46beff6b", "text": "#19152c", "text.muted": "#585d74", "text.placeholder": "#7f8a99", "text.accent": "#46BDFF", "icon.accent": "#46BDFF", "status_bar.background": "#e4e5f5", "title_bar.background": "#e4e5f5", "toolbar.background": "#e4e5f5", "tab_bar.background": "#dfe0f0", "tab.inactive_background": "#dfe0f0", "tab.active_background": "#e4e5f5", "panel.background": "#dfe0f0", "scrollbar.thumb.background": "#4E566680", "scrollbar.thumb.hover_background": "#5A637580", "scrollbar.track.background": "#e4e5f5", "editor.foreground": "#19152c", "editor.background": "#e4e5f5", "editor.gutter.background": "#e4e5f5", "editor.active_line.background": "#dcddec", "editor.line_number": "#7f8a99", "editor.active_line_number": "#19152c", "editor.invisible": "#f1f1f1", "editor.wrap_guide": "#f1f1f1", "editor.active_wrap_guide": "#f1f1f1", "terminal.background": "#e4e5f5", "terminal.foreground": "#19152c", "terminal.ansi.black": "#19152c", "terminal.ansi.bright_black": "#585d74", "terminal.ansi.red": "#FF407B", "terminal.ansi.bright_red": "#FF407B", "terminal.ansi.green": "#00af4f", "terminal.ansi.bright_green": "#00af4f", "terminal.ansi.yellow": "#FF8D01", "terminal.ansi.bright_yellow": "#FF8D01", "terminal.ansi.blue": "#0098fd", "terminal.ansi.bright_blue": "#0098fd", "terminal.ansi.magenta": "#FF16B0", "terminal.ansi.bright_magenta": "#FF16B0", "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.white": "#ffffff", "terminal.ansi.bright_white": "#ffffff", "conflict": "#FF407B", "created": "#0098fd", "deleted": "#FF16B0", "error": "#FF407B", "error.background": "#dfe0f0", "hidden": "#adadad", "hint": "#00000052", "hint.background": "#dfe0f0", "ignored": "#adadad", "info": "#0098fd", "info.background": "#dfe0f0", "modified": "#FF8D01", "predictive": "#585d74", "success": "#00af4f", "success.background": "#dfe0f0", "warning": "#FF8D01", "warning.background": "#dfe0f0", "players": [{"cursor": "#FF16B0ff", "background": "#FF16B0ff", "selection": "#FF16B03d"}, {"cursor": "#0098fdff", "background": "#0098fdff", "selection": "#0098fd3d"}, {"cursor": "#4d5effff", "background": "#4d5effff", "selection": "#4d5eff3d"}, {"cursor": "#c300ffff", "background": "#c300ffff", "selection": "#c300ff3d"}, {"cursor": "#FF8D01ff", "background": "#FF8D01ff", "selection": "#FF8D013d"}, {"cursor": "#00af4fff", "background": "#00af4fff", "selection": "#00af4f3d"}, {"cursor": "#1930fdff", "background": "#1930fdff", "selection": "#1930fd3d"}, {"cursor": "#9793b9ff", "background": "#9793b9ff", "selection": "#9793b93d"}], "syntax": {"comment": {"color": "#9793b9", "font_style": "italic"}, "comment.doc": {"color": "#9793b9", "font_style": "italic"}, "string": {"color": "#c300ff"}, "string.escape": {"color": "#19152c"}, "string.regex": {"color": "#4d5eff"}, "string.special": {"color": "#19152c"}, "string.special.symbol": {"color": "#19152c"}, "variable": {"color": "#4d5eff"}, "variable.special": {"color": "#4d5eff"}, "constant": {"color": "#4d5eff"}, "number": {"color": "#c300ff"}, "boolean": {"color": "#c300ff"}, "type": {"color": "#0098fd"}, "function": {"color": "#00af4f"}, "keyword": {"color": "#FF16B0"}, "operator": {"color": "#c300ff"}, "punctuation": {"color": "#19152c"}, "tag": {"color": "#0098fd"}, "attribute": {"color": "#4d5eff"}, "constructor": {"color": "#0098fd"}, "embedded": {"color": "#19152c"}, "link_text": {"color": "#19152c"}, "link_uri": {"color": "#00af4f"}, "title": {"color": "#00af4f"}, "text.literal": {"color": "#c300ff"}, "property": {"color": "#0098fd"}}}}]}