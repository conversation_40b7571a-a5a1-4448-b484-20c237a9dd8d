{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984 - Orwellian Edition", "author": "converted from VS Code theme", "themes": [{"name": "1984 - Orwellian Edition", "appearance": "dark", "style": {"border": "#292521", "border.variant": "#292521", "border.focused": "#fcd395", "border.selected": "#fcd395", "border.transparent": "#292521", "border.disabled": "#292521", "elevated_surface.background": "#2e2923", "surface.background": "#2e2923", "background": "#2e2923", "element.background": "#fcd39552", "element.hover": "#fcd3954f", "element.active": "#fcd39552", "element.selected": "#fcd39552", "drop_target.background": "#fcd39512", "ghost_element.hover": "#fcd3954f", "ghost_element.selected": "#fcd39552", "text": "#f1f1f1", "text.muted": "#fcd395", "text.placeholder": "#fcda9575", "text.accent": "#fcd395", "icon.accent": "#fcd395", "status_bar.background": "#2e2923", "title_bar.background": "#2e2923", "toolbar.background": "#2e2923", "tab_bar.background": "#292521", "tab.inactive_background": "#292521", "tab.active_background": "#2e2923", "panel.background": "#2e2923", "pane.focused_border": "#fcd395", "scrollbar.thumb.background": "#fcda9523", "scrollbar.thumb.hover_background": "#fcda9523", "scrollbar.track.background": "#2e2923", "editor.foreground": "#f1f1f1", "editor.background": "#2e2923", "editor.gutter.background": "#2e2923", "editor.line_number": "#fcda9575", "editor.active_line_number": "#fff", "editor.invisible": "#fcda9523", "editor.wrap_guide": "#fcda9523", "editor.active_wrap_guide": "#fcda9523", "terminal.background": "#2e2923", "terminal.foreground": "#f1f1f1", "terminal.ansi.black": "#292521", "terminal.ansi.bright_black": "#4E5666", "terminal.ansi.red": "#e74946", "terminal.ansi.bright_red": "#e74946", "terminal.ansi.green": "#4cb605", "terminal.ansi.bright_green": "#4cb605", "terminal.ansi.yellow": "#fcd395", "terminal.ansi.bright_yellow": "#fcd395", "terminal.ansi.blue": "#bcc8e0", "terminal.ansi.bright_blue": "#bcc8e0", "terminal.ansi.magenta": "#e74946", "terminal.ansi.bright_magenta": "#e74946", "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.white": "#f1f1f1", "terminal.ansi.bright_white": "#ffffff", "conflict": "#e74946", "created": "#bcc8e0", "deleted": "#e74946", "error": "#e74946", "error.background": "#2e2923", "hidden": "#D5D8DA59", "hint": "#ffffff72", "hint.background": "#2e2923", "ignored": "#D5D8DA59", "info": "#bcc8e0", "info.background": "#2e2923", "modified": "#fcd395", "predictive": "#fcd395", "success": "#4cb605", "success.background": "#2e2923", "warning": "#fcd395", "warning.background": "#2e2923", "players": [{"cursor": "#fcd395ff", "background": "#fcd395ff", "selection": "#fcd3953d"}, {"cursor": "#e74946ff", "background": "#e74946ff", "selection": "#e749463d"}, {"cursor": "#4cb605ff", "background": "#4cb605ff", "selection": "#4cb6053d"}, {"cursor": "#bcc8e0ff", "background": "#bcc8e0ff", "selection": "#bcc8e03d"}, {"cursor": "#fcbe95ff", "background": "#fcbe95ff", "selection": "#fcbe953d"}, {"cursor": "#F7D88Bff", "background": "#F7D88Bff", "selection": "#F7D88B3d"}, {"cursor": "#F2F2F2ff", "background": "#F2F2F2ff", "selection": "#F2F2F23d"}, {"cursor": "#fcbe954fff", "background": "#fcbe954fff", "selection": "#fcbe954f3d"}], "syntax": {"comment": {"color": "#fcbe954f", "font_style": "italic", "font_weight": null}, "comment.doc": {"color": "#fcbe954f", "font_style": "italic", "font_weight": null}, "string": {"color": "#fcbe95", "font_style": null, "font_weight": null}, "string.escape": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "string.regex": {"color": "#fcbe95", "font_style": null, "font_weight": null}, "string.special": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "variable": {"color": "#fcd395", "font_style": null, "font_weight": null}, "variable.special": {"color": "#fcd395", "font_style": null, "font_weight": null}, "constant": {"color": "#fcbe95", "font_style": null, "font_weight": null}, "number": {"color": "#fcbe95", "font_style": null, "font_weight": 600}, "boolean": {"color": "#fcbe95", "font_style": null, "font_weight": null}, "type": {"color": "#bcc8e0", "font_style": null, "font_weight": 600}, "function": {"color": "#4cb605", "font_style": null, "font_weight": 600}, "keyword": {"color": "#e74946", "font_style": null, "font_weight": 600}, "operator": {"color": "#fcbe95", "font_style": null, "font_weight": 600}, "punctuation": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "tag": {"color": "#bcc8e0", "font_style": null, "font_weight": 600}, "attribute": {"color": "#fcd395", "font_style": null, "font_weight": null}, "constructor": {"color": "#bcc8e0", "font_style": null, "font_weight": 600}, "embedded": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "link_text": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "link_uri": {"color": "#4cb605", "font_style": null, "font_weight": null}, "title": {"color": "#4cb605", "font_style": null, "font_weight": null}, "text.literal": {"color": "#fcbe95", "font_style": null, "font_weight": 600}, "property": {"color": "#bcc8e0", "font_style": null, "font_weight": null}}}}]}