{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984", "author": "converted from VS Code theme", "themes": [{"name": "1984", "appearance": "dark", "style": {"border": "#181A1F", "border.variant": "#181A1F", "border.focused": "#46BDFF", "border.selected": "#46BDFF", "border.transparent": "#181A1F", "border.disabled": "#181A1F", "elevated_surface.background": "#070825", "surface.background": "#070825", "background": "#0d0f31", "element.background": "#2C313A", "element.hover": "#2C313A", "element.active": "#2C313A", "element.selected": "#2C313A", "drop_target.background": "#45beff12", "ghost_element.hover": "#2C313A", "ghost_element.selected": "#2C313A", "text": "#f1f1f1", "text.muted": "#b5becf", "text.placeholder": "#7b7f86", "text.accent": "#46BDFF", "icon.accent": "#46BDFF", "status_bar.background": "#070825", "title_bar.background": "#070825", "toolbar.background": "#0d0f31", "tab_bar.background": "#070825", "tab.inactive_background": "#070825", "tab.active_background": "#0d0f31", "panel.background": "#070825", "pane.focused_border": "#46BDFF", "scrollbar.thumb.background": "#4E566680", "scrollbar.thumb.hover_background": "#5A637580", "scrollbar.track.background": "#0d0f31", "editor.foreground": "#f1f1f1", "editor.background": "#0d0f31", "editor.gutter.background": "#0d0f31", "editor.line_number": "#3B4D66", "editor.active_line_number": "#f1f1f1", "editor.invisible": "#2c394c8f", "editor.wrap_guide": "#2c394c8f", "editor.active_wrap_guide": "#2c394c8f", "terminal.background": "#0d0f31", "terminal.foreground": "#f1f1f1", "terminal.ansi.black": "#070825", "terminal.ansi.bright_black": "#4E5666", "terminal.ansi.red": "#FF407B", "terminal.ansi.bright_red": "#FF407B", "terminal.ansi.green": "#B3F361", "terminal.ansi.bright_green": "#B3F361", "terminal.ansi.yellow": "#FFEA16", "terminal.ansi.bright_yellow": "#FFEA16", "terminal.ansi.blue": "#46BDFF", "terminal.ansi.bright_blue": "#46BDFF", "terminal.ansi.magenta": "#FF16B0", "terminal.ansi.bright_magenta": "#FF16B0", "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.white": "#f1f1f1", "terminal.ansi.bright_white": "#ffffff", "conflict": "#FF407B", "created": "#46BDFF", "deleted": "#FF16B0", "error": "#FF407B", "error.background": "#2C313A", "hidden": "#7b7f86", "hint": "#ffffff72", "hint.background": "#2C313A", "ignored": "#D5D8DA59", "info": "#46BDFF", "info.background": "#2C313A", "modified": "#fcee54", "predictive": "#b5becf", "success": "#B3F361", "success.background": "#2C313A", "warning": "#FFEA16", "warning.background": "#2C313A", "players": [{"cursor": "#B3F361ff", "background": "#B3F361ff", "selection": "#B3F3613d"}, {"cursor": "#46BDFFff", "background": "#46BDFFff", "selection": "#46BDFF3d"}, {"cursor": "#FF16B0ff", "background": "#FF16B0ff", "selection": "#FF16B03d"}, {"cursor": "#FF407Bff", "background": "#FF407Bff", "selection": "#FF407B3d"}, {"cursor": "#FFEA16ff", "background": "#FFEA16ff", "selection": "#FFEA163d"}, {"cursor": "#59E1E3ff", "background": "#59E1E3ff", "selection": "#59E1E33d"}, {"cursor": "#6BE4E6ff", "background": "#6BE4E6ff", "selection": "#6BE4E63d"}, {"cursor": "#96A1FFff", "background": "#96A1FFff", "selection": "#96A1FF3d"}], "syntax": {"comment": {"color": "#525863", "font_style": "italic", "font_weight": 600}, "comment.doc": {"color": "#525863", "font_style": "italic", "font_weight": 600}, "string": {"color": "#DF81FC", "font_style": null, "font_weight": 600}, "string.escape": {"color": "#fcfcfc", "font_style": null, "font_weight": 600}, "string.regex": {"color": "#96A1FF", "font_style": null, "font_weight": 600}, "string.special": {"color": "#fcfcfc", "font_style": null, "font_weight": 600}, "string.special.symbol": {"color": "#fcfcfc", "font_style": null, "font_weight": 600}, "variable": {"color": "#96A1FF", "font_style": null, "font_weight": 600}, "variable.special": {"color": "#46BDFF", "font_style": null, "font_weight": 600}, "constant": {"color": "#96A1FF", "font_style": null, "font_weight": 600}, "number": {"color": "#DF81FC", "font_style": null, "font_weight": 600}, "boolean": {"color": "#DF81FC", "font_style": null, "font_weight": 600}, "type": {"color": "#46BDFF", "font_style": null, "font_weight": 600}, "function": {"color": "#B3F361", "font_style": null, "font_weight": 600}, "keyword": {"color": "#FF16B0", "font_style": null, "font_weight": 600}, "operator": {"color": "#DF81FC", "font_style": null, "font_weight": 600}, "punctuation": {"color": "#bcd4cf", "font_style": null, "font_weight": 600}, "tag": {"color": "#46BDFF", "font_style": null, "font_weight": 600}, "attribute": {"color": "#BCD4CF", "font_style": null, "font_weight": 600}, "constructor": {"color": "#46BDFF", "font_style": null, "font_weight": 600}, "embedded": {"color": "#fcfcfc", "font_style": null, "font_weight": 600}, "link_text": {"color": "#F2F2F2", "font_style": null, "font_weight": 600}, "link_uri": {"color": "#B3F361", "font_style": null, "font_weight": 600}, "title": {"color": "#B3F361", "font_style": null, "font_weight": 600}, "text.literal": {"color": "#DF81FC", "font_style": null, "font_weight": 600}, "property": {"color": "#46BDFF", "font_style": null, "font_weight": 600}}}}]}