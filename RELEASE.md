# 自动化发布指南

## 🚀 发布方式

### 方式1：自动发布（推荐）

推送到 `release` 分支时自动触发发布：

```bash
# 合并更改到 release 分支
git checkout release
git merge main
git push origin release
```

系统会按以下顺序自动执行：
1. **版本分析**：检测提交信息确定版本类型
2. **版本更新**：更新 `extension.toml` 版本号并验证
3. **提交变更**：提交版本更新到 git
4. **创建标签**：创建指向正确 commit 的 git tag
5. **推送更新**：先推送分支更新，再推送 tag
6. **一致性验证**：确保版本号和 tag 完全一致
7. **发布插件**：发布到 `chenmijiang/zed-extensions`
8. **创建发布**：创建 GitHub Release

### 方式2：手动发布

在 GitHub Actions 页面手动触发，可选择版本类型：

1. 访问 [Actions 页面](https://github.com/chenmijiang/zed-1984/actions/workflows/release.yml)
2. 点击 "Run workflow"
3. 选择版本类型：
   - **patch** (0.0.X) - 修复bug
   - **minor** (0.X.0) - 新功能
   - **major** (X.0.0) - 重大变更

## 📋 版本自动检测规则

推送到 `release` 分支时，系统会分析最近的提交信息：

| 提交信息包含 | 版本类型 | 示例 |
|-------------|----------|------|
| `breaking`, `major` | major (1.0.0) | `fix: breaking change in theme structure` |
| `feat`, `feature`, `minor` | minor (0.1.0) | `feat: add new cyberpunk variant` |
| 其他 | patch (0.0.1) | `fix: color contrast issue` |

## 🔧 使用流程

### 日常开发
```bash
# 在 main 分支开发
git checkout main
git add .
git commit -m "feat: add new theme colors"
git push origin main
```

### 发布新版本
```bash
# 方法1：直接在 release 分支开发
git checkout release
git add .
git commit -m "feat: add dark mode support"
git push origin release

# 方法2：从 main 分支合并
git checkout release
git merge main
git push origin release
```

## 📊 当前状态

- **当前版本**: 0.0.1
- **发布分支**: release
- **目标仓库**: chenmijiang/zed-extensions

## 🛠️ 设置要求

确保 GitHub 仓库配置了以下 Secret：
- `COMMITTER_TOKEN`: 用于推送到目标仓库的个人访问令牌

## 🔧 工作流程保障

### 版本一致性保证
- ✅ 版本更新后立即验证
- ✅ Tag 创建前确保 commit 包含正确版本
- ✅ 发布前进行完整性检查
- ✅ 推送顺序：分支更新 → Tag 推送

### 错误处理机制
- 🛡️ 插件发布失败不会中断整个流程
- 🛡️ 版本不一致会立即终止流程
- 🛡️ 每个关键步骤都有验证和日志

## 📞 故障排除

### 发布失败
1. **插件发布失败**：
   - 检查 `COMMITTER_TOKEN` 权限
   - 确认目标仓库 `chenmijiang/zed-extensions` 可访问
   - GitHub Release 仍会正常创建

2. **版本一致性错误**：
   - 检查 `extension.toml` 格式
   - 确认版本号格式为 `x.y.z`
   - 查看 Actions 日志中的详细错误信息

3. **Tag 创建失败**：
   - 检查是否已存在同名 tag
   - 确认分支推送权限

### 版本号错误
1. 检查提交信息是否包含正确的关键词
2. 使用手动发布指定版本类型
3. 手动编辑 `extension.toml` 后重新推送

### 回滚版本
```bash
# 回滚到上一个版本
git checkout release
git reset --hard <previous-commit>
git push --force-with-lease origin release

# 删除错误的 tag（如果已创建）
git tag -d v<wrong-version>
git push origin :refs/tags/v<wrong-version>
```

### 监控发布状态
- 查看 [Actions 页面](https://github.com/chenmijiang/zed-1984/actions) 获取详细日志
- 检查 [Releases 页面](https://github.com/chenmijiang/zed-1984/releases) 确认发布状态
- 验证 [目标仓库](https://github.com/chenmijiang/zed-extensions) 是否收到更新
