on:
  push:
    branches:
      - release
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

permissions:
  contents: write

jobs:
  auto-release:
    name: Auto Release Zed Extension
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get current version
        id: current_version
        run: |
          VERSION=$(grep '^version = ' extension.toml | sed 's/version = "\(.*\)"/\1/')
          echo "current=$VERSION" >> $GITHUB_OUTPUT
          echo "Current version: $VERSION"

      - name: Determine version bump
        id: version_bump
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            BUMP_TYPE="${{ github.event.inputs.version_type }}"
          else
            COMMITS=$(git log --oneline $(git describe --tags --abbrev=0 2>/dev/null || echo "HEAD~10")..HEAD 2>/dev/null || git log --oneline -10)
            if echo "$COMMITS" | grep -i "breaking\|major"; then
              BUMP_TYPE="major"
            elif echo "$COMMITS" | grep -i "feat\|feature\|minor"; then
              BUMP_TYPE="minor"
            else
              BUMP_TYPE="patch"
            fi
          fi
          echo "type=$BUMP_TYPE" >> $GITHUB_OUTPUT
          echo "Version bump type: $BUMP_TYPE"

      - name: Calculate new version
        id: new_version
        run: |
          CURRENT="${{ steps.current_version.outputs.current }}"
          BUMP_TYPE="${{ steps.version_bump.outputs.type }}"

          IFS='.' read -r major minor patch <<< "$CURRENT"

          case $BUMP_TYPE in
            major)
              major=$((major + 1))
              minor=0
              patch=0
              ;;
            minor)
              minor=$((minor + 1))
              patch=0
              ;;
            patch)
              patch=$((patch + 1))
              ;;
          esac

          NEW_VERSION="$major.$minor.$patch"
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "New version: $NEW_VERSION"

      - name: Update extension.toml
        run: |
          sed -i 's/version = ".*"/version = "${{ steps.new_version.outputs.version }}"/' extension.toml
          echo "Updated extension.toml to version ${{ steps.new_version.outputs.version }}"

          # 验证更新是否成功
          UPDATED_VERSION=$(grep '^version = ' extension.toml | sed 's/version = "\(.*\)"/\1/')
          if [ "$UPDATED_VERSION" != "${{ steps.new_version.outputs.version }}" ]; then
            echo "Error: Version update failed. Expected ${{ steps.new_version.outputs.version }}, got $UPDATED_VERSION"
            exit 1
          fi
          echo "Version update verified: $UPDATED_VERSION"

      - name: Commit and tag version update
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          # 添加并提交版本更新
          git add extension.toml
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "chore: bump version to ${{ steps.new_version.outputs.version }}"
            echo "Version update committed"
          fi

          # 创建 tag（指向包含正确版本的 commit）
          git tag "v${{ steps.new_version.outputs.version }}"
          echo "Created tag v${{ steps.new_version.outputs.version }}"

      - name: Push changes and tag
        run: |
          # 先推送分支更新（包含版本变更的 commit）
          git push origin release
          echo "Pushed branch updates"

          # 再推送 tag（确保 tag 指向正确的 commit）
          git push origin "v${{ steps.new_version.outputs.version }}"
          echo "Pushed tag v${{ steps.new_version.outputs.version }}"

      - name: Verify version consistency
        run: |
          # 验证 extension.toml 中的版本
          TOML_VERSION=$(grep '^version = ' extension.toml | sed 's/version = "\(.*\)"/\1/')

          # 验证 git tag 是否存在且指向当前 commit
          CURRENT_COMMIT=$(git rev-parse HEAD)
          TAG_COMMIT=$(git rev-parse "v${{ steps.new_version.outputs.version }}" 2>/dev/null || echo "")

          echo "Expected version: ${{ steps.new_version.outputs.version }}"
          echo "TOML version: $TOML_VERSION"
          echo "Current commit: $CURRENT_COMMIT"
          echo "Tag commit: $TAG_COMMIT"

          # 检查版本一致性
          if [ "$TOML_VERSION" != "${{ steps.new_version.outputs.version }}" ]; then
            echo "❌ Error: extension.toml version mismatch"
            exit 1
          fi

          # 检查 tag 是否指向当前 commit
          if [ "$TAG_COMMIT" != "$CURRENT_COMMIT" ]; then
            echo "❌ Error: Tag does not point to current commit"
            exit 1
          fi

          echo "✅ Version consistency verified"

      - name: Publish to Zed Extensions
        id: publish_extension
        uses: huacnlee/zed-extension-action@v1
        with:
          extension-name: 1984-theme
          push-to: chenmijiang/zed-extensions
          create-pullrequest: false
        env:
          COMMITTER_TOKEN: ${{ secrets.COMMITTER_TOKEN }}
        continue-on-error: true

      - name: Handle extension publish failure
        if: steps.publish_extension.outcome == 'failure'
        run: |
          echo "❌ Extension publishing failed, but continuing with GitHub release"
          echo "Please check the extension publishing manually"
          echo "Extension version: ${{ steps.new_version.outputs.version }}"
          echo "Target repository: chenmijiang/zed-extensions"

      - name: Create GitHub Release
        id: create_release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.new_version.outputs.version }}
          name: Release v${{ steps.new_version.outputs.version }}
          body: |
            ## Changes in v${{ steps.new_version.outputs.version }}

            Auto-generated release from release branch.

            **Extension Status:** ${{ steps.publish_extension.outcome == 'success' && '✅ Published to Zed Extensions' || '⚠️ Extension publishing failed - manual intervention required' }}

            View the full changelog and commit history for detailed changes.
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Summary
        run: |
          echo "🎉 Release v${{ steps.new_version.outputs.version }} completed!"
          echo ""
          echo "📋 Release Summary:"
          echo "  - Version: ${{ steps.new_version.outputs.version }}"
          echo "  - Tag: v${{ steps.new_version.outputs.version }}"
          echo "  - Extension Publishing: ${{ steps.publish_extension.outcome }}"
          echo "  - GitHub Release: ${{ steps.create_release.outcome }}"
          echo ""
          if [ "${{ steps.publish_extension.outcome }}" = "failure" ]; then
            echo "⚠️  Manual action required: Check extension publishing to chenmijiang/zed-extensions"
          else
            echo "✅ All steps completed successfully!"
          fi
