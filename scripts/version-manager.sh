#!/bin/bash

# Version Manager Script
# Handles version extraction, calculation, and validation for Zed extension releases
# Usage: ./version-manager.sh <command> [arguments]

set -euo pipefail

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly EXTENSION_TOML="$PROJECT_ROOT/extension.toml"
readonly LOG_PREFIX="[VERSION-MANAGER]"

# Logging functions
log_info() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') INFO $LOG_PREFIX $*" >&2
}

log_error() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') ERROR $LOG_PREFIX $*" >&2
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') DEBUG $LOG_PREFIX $*" >&2
    fi
}

# Validation functions
validate_extension_toml() {
    if [[ ! -f "$EXTENSION_TOML" ]]; then
        log_error "extension.toml not found at: $EXTENSION_TOML"
        return 1
    fi
    
    if ! grep -q '^version = ' "$EXTENSION_TOML"; then
        log_error "No version field found in extension.toml"
        return 1
    fi
    
    log_debug "extension.toml validation passed"
    return 0
}

validate_version_format() {
    local version="$1"
    
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "Invalid version format: $version (expected: x.y.z)"
        return 1
    fi
    
    log_debug "Version format validation passed: $version"
    return 0
}

# Core functions
get_current_version() {
    validate_extension_toml || return 1
    
    local version
    version=$(grep '^version = ' "$EXTENSION_TOML" | sed 's/version = "\(.*\)"/\1/')
    
    if [[ -z "$version" ]]; then
        log_error "Failed to extract version from extension.toml"
        return 1
    fi
    
    validate_version_format "$version" || return 1
    
    log_info "Current version: $version"
    echo "$version"
}

calculate_new_version() {
    local current_version="$1"
    local bump_type="$2"
    
    validate_version_format "$current_version" || return 1
    
    if [[ ! "$bump_type" =~ ^(major|minor|patch)$ ]]; then
        log_error "Invalid bump type: $bump_type (expected: major, minor, or patch)"
        return 1
    fi
    
    IFS='.' read -r major minor patch <<< "$current_version"
    
    case "$bump_type" in
        major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        minor)
            minor=$((minor + 1))
            patch=0
            ;;
        patch)
            patch=$((patch + 1))
            ;;
    esac
    
    local new_version="$major.$minor.$patch"
    validate_version_format "$new_version" || return 1
    
    log_info "Version bump ($bump_type): $current_version -> $new_version"
    echo "$new_version"
}

update_extension_version() {
    local new_version="$1"
    
    validate_version_format "$new_version" || return 1
    validate_extension_toml || return 1
    
    log_info "Updating extension.toml to version: $new_version"
    
    # Create backup
    cp "$EXTENSION_TOML" "$EXTENSION_TOML.backup"
    
    # Update version
    if ! sed -i.tmp "s/^version = \".*\"/version = \"$new_version\"/" "$EXTENSION_TOML"; then
        log_error "Failed to update version in extension.toml"
        # Restore backup
        mv "$EXTENSION_TOML.backup" "$EXTENSION_TOML"
        return 1
    fi
    
    # Clean up temporary files
    rm -f "$EXTENSION_TOML.tmp" "$EXTENSION_TOML.backup"
    
    # Verify update
    local updated_version
    updated_version=$(get_current_version)
    
    if [[ "$updated_version" != "$new_version" ]]; then
        log_error "Version update verification failed. Expected: $new_version, Got: $updated_version"
        return 1
    fi
    
    log_info "Version successfully updated and verified: $new_version"
    return 0
}

verify_version_consistency() {
    local expected_version="$1"
    local tag_name="v$expected_version"
    
    validate_version_format "$expected_version" || return 1
    
    log_info "Verifying version consistency for: $expected_version"
    
    # Check extension.toml version
    local toml_version
    toml_version=$(get_current_version) || return 1
    
    if [[ "$toml_version" != "$expected_version" ]]; then
        log_error "extension.toml version mismatch. Expected: $expected_version, Got: $toml_version"
        return 1
    fi
    
    # Check git tag consistency
    local current_commit tag_commit
    current_commit=$(git rev-parse HEAD)
    
    if git rev-parse --verify "$tag_name" >/dev/null 2>&1; then
        tag_commit=$(git rev-parse "$tag_name")
        
        if [[ "$tag_commit" != "$current_commit" ]]; then
            log_error "Tag $tag_name does not point to current commit"
            log_error "Current commit: $current_commit"
            log_error "Tag commit: $tag_commit"
            return 1
        fi
        
        log_info "Tag $tag_name points to current commit: $current_commit"
    else
        log_error "Tag $tag_name does not exist"
        return 1
    fi
    
    log_info "Version consistency verification passed"
    return 0
}

# Command dispatcher
main() {
    local command="${1:-}"
    
    case "$command" in
        get-current)
            get_current_version
            ;;
        calculate)
            if [[ $# -ne 3 ]]; then
                log_error "Usage: $0 calculate <current_version> <bump_type>"
                exit 1
            fi
            calculate_new_version "$2" "$3"
            ;;
        update)
            if [[ $# -ne 2 ]]; then
                log_error "Usage: $0 update <new_version>"
                exit 1
            fi
            update_extension_version "$2"
            ;;
        verify)
            if [[ $# -ne 2 ]]; then
                log_error "Usage: $0 verify <expected_version>"
                exit 1
            fi
            verify_version_consistency "$2"
            ;;
        *)
            log_error "Unknown command: $command"
            log_error "Available commands: get-current, calculate, update, verify"
            exit 1
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
